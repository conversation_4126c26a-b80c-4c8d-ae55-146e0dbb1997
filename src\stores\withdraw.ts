import { defineStore } from "pinia";
import { getWithdrawAccounts, goExchange, checkWithdrawRisk } from "@/api/withdrawal";
import { rechargeWithdraw } from "@/api/deposit";

import { useGlobalStore } from "@/stores/global";
import { useKycMgrStore, KYC_SCENE, KYC_TYPE, KYC_STATUS } from "@/stores/kycMgr";
import { useGameStore } from "@/stores/game";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";

import { showToast } from "vant";
import { CHANEL_TYPE, PRODUCT_TYPE, MAINTENANCETIPCODE } from "@/utils/config/GlobalConstant";
import { GeetestMgr, GEETEST_TYPE } from "@/utils/GeetestMgr";
import { executeVerification, type VerificationResult } from "@/utils/VerificationMgr";
import router from "@/router";
import { Md5 } from "@/utils/core/Md5";
import { getGlobalDialog } from "@/enter/vant";
import { maskAccountNumber } from "@/utils/core/tools";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import enTranslations from "@/utils/I18n/en.json";
import { METHODS_NAMES, METHODS_ID } from "@/utils/config/GlobalConstant";
import { useVerifyPreconditions } from "@/composables/useVerifyPreconditions";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";
import { verifyLoginPassword } from "@/utils/HttpProxy";
import { SELECT_ACCOUNT_TYPE } from "@/utils/config/GlobalConstant";
import { getLocalStorage, setLocalStorage } from "@/utils/core/Storage";

// 风控信息接口
export interface RiskWithdrawalInfo {
  status: number;
  user_id: string;
  user_total_valid_bet: number;
  target_amount: number;
}

// 提现数据接口
interface WithdrawData {
  min: number;
  max: number;
  sort: number;
  name: string;
  account_type?: number;
  icon?: string;
  tags?: number[];
}

// 账户信息接口
interface AccountInfo {
  account_id?: string;
  account_no?: string;
  name?: string;
  type?: number;
}

export const useWithdrawStore = defineStore("withdraw", {
  state: () => {
    return {
      // 已选中的账户信息
      selectedAccountInfo: null as AccountInfo | null,
      //弹窗：显示提现弹
      showWithdrawDialog: false,
      //弹窗：显示账户选择列表
      showAccountListDialog: false,
      //弹窗：小程序内金额确认
      showMoneyConfirmDialog: false,
      // 弹窗：1倍流水风控制
      showRiskWithdrawalDialog: false,
      // 弹窗：设置手机号、登录密码
      showVerifyPreconditionsDialog: false,
      // 弹窗：输入登录密码
      showVerifyLoginPasswordDialog: false,
      // 弹窗：密码次数过多限制
      showPaymentPassworLimtDialog: false,
      // 弹窗：KYC 二次提醒
      showKycWithdralDialog: false,
      // 账户列表
      accounts: [] as AccountInfo[],
      // 账户列表限制
      accountLimitNum: 10,
      // 提现方式列表
      withdrawData: [] as WithdrawData[],
      // 当前提现方式的可提的最大、最小值
      minAmountNum: null as number | null,
      maxAmountNum: null as number | null,
      // 输入（选中）的充值金额
      selectedAmount: "",
      // 当前银行卡归宿，Maya、Gcash
      curRechangeMethods: "",
      // 错误提示
      errTip: "",
      // 警告提示
      warnTip: "",
      // 1倍流水风控信息
      riskWithdrawalInfo: null as RiskWithdrawalInfo | null,
      // 次数限制 email
      customerEmail: "",
      riskWithdrawalCallBack: () => {},
    };
  },
  getters: {
    // 渠道来源
    CHANEL_TYPE() {
      const globalStore = useGlobalStore();
      return globalStore.channel;
    },
    // 是否小程序端，即 G_CASH、MAYA 端
    isMiniChannel() {
      const globalStore = useGlobalStore();
      const channel = globalStore.channel?.toLowerCase();
      return ["gcash", "maya"].includes(channel);
    },
    $dialog() {
      return getGlobalDialog();
    },
    // 是否需要登录密码
    isNeedLoginPassword() {
      return useGameStore().configData.edit_login_password_switch == "1";
    },
    kycMgrStore() {
      return useKycMgrStore();
    },
    autoPopMgrStore() {
      return useAutoPopMgrStore();
    },
  },
  actions: {
    /**
     * 打开提现入口
     * 根据用户登录状态、渠道类型和账户情况决定后续操作
     */
    async openDialog() {
      const globalStore = useGlobalStore();
      if (!globalStore.token) {
        router.push("/login");
        return;
      }
      showZLoading();
      try {
        if (!this.isMiniChannel) {
          const accountList = await this.getWithdrawAccountList();
          const hasAccounts = accountList.length > 0;
          if (hasAccounts) {
            //有提现账户跳转提现页面
            const withdrawList = await this.getWithdrawConfigList(() => {
              this.selectFirstAccount();
              this.updateWithdrawRangeForSelectedAccount();
            });
            if (this.hasValidAccountList(accountList, withdrawList)) {
              // 风控
              this.handleRiskWithdrawalDialog(() => {
                this.kycMgrStore.verifyKyc(KYC_SCENE.Withdraw, async (isVerity) => {
                  if (isVerity) {
                    this.showWithdrawDialog = true;
                  }
                });
              });
            } else {
              // 无有效的账户
              this.handleRiskWithdrawalDialog(() => {
                router.push("/account/withdraw-account");
              });
            }
          } else {
            //无提现账户
            router.push("/account/withdraw-account");
            // this.handleRiskWithdrawalDialog(() => {
            //   router.push("/account/withdraw-account");
            // });
          }
        } else {
          await this.getWithdrawConfigList(() => {
            this.selectFirstAccount();
            this.updateWithdrawRangeForSelectedAccount();
          });
          // 默认选择账号
          this.handleMiniChannelAccount();
          // 风控
          this.handleRiskWithdrawalDialog(() => {
            this.showWithdrawDialog = true;
          });
        }
      } catch (err) {
        this.handleLoadError();
      } finally {
        closeZLoading();
      }
    },

    //判断是否有可以用的提现账户 账户列表 和 提现方式
    hasValidAccountList(accountList, withdrawList) {
      const validBankIds = new Set(withdrawList.map((bank) => bank.account_type));
      return accountList.some((card) => validBankIds.has(card.type));
    },
    existWithdrawMethod(accountType: number) {
      const validBankIds = new Set(this.withdrawData.map((bank) => bank.account_type));
      return validBankIds.has(accountType);
    },

    /**
     * 处理小程序渠道的默认账户
     */
    handleMiniChannelAccount() {
      const globalStore = useGlobalStore();
      this.selectedAccountInfo = {
        account_no: maskAccountNumber(globalStore.userInfo.phone || ""),
        name: globalStore.channel,
        type: METHODS_ID[globalStore.channel as keyof typeof METHODS_ID],
      };
      this.curRechangeMethods = globalStore.channel;
    },

    /**
     * 处理加载错误
     */
    handleLoadError(error?: any) {
      // 重置状态
      this.withdrawData = [];
      this.accounts = [];
      this.showWithdrawDialog = false;
      // 显示错误提示
      showToast("The current network is abnormal, please try again later.");
    },

    /**
     * 选择第一个可用账户
     * 优先级：用户上次选择的账户 > GCash > Maya
     */
    async selectFirstAccount() {
      const list = this.accounts;
      if (!list || list.length === 0) {
        return;
      }
      // 获取用户上次选择的账户ID
      const savedAccountId = getLocalStorage(SELECT_ACCOUNT_TYPE);

      // 定义账户选择优先级
      const accountPriority = [
        // 1. 用户上次选择的账户（最高优先级）
        (account: AccountInfo) => {
          if (savedAccountId && String(savedAccountId).indexOf(account.account_id) > -1) {
            return true;
          }
          return false;
        },

        // 2. GCash 账户
        (account: AccountInfo) => {
          if (account.type === METHODS_ID.GCash && this.existWithdrawMethod(account.type)) {
            return true;
          }
          return false;
        },
        // 3. Maya 账户
        (account: AccountInfo) => {
          if (account.type === METHODS_ID.Maya && this.existWithdrawMethod(account.type)) {
            return true;
          }
          return false;
        },
      ];

      // 按优先级查找并选择账户
      for (const priorityCheck of accountPriority) {
        const selectedAccount = list.find((account) => {
          // 检查账户是否符合当前优先级条件
          if (!priorityCheck(account)) {
            return false;
          }
          return true;
        });

        if (selectedAccount) {
          // 设置选中的账户信息
          this.setSelectedAccount(selectedAccount);
          return;
        }
      }
    },

    /**
     * 设置选中的账户信息
     */
    setSelectedAccount(account: AccountInfo) {
      this.selectedAccountInfo = {
        ...account,
        name: METHODS_NAMES[account.type],
      };
      this.curRechangeMethods = METHODS_NAMES[account.type];

      // 更新提现范围
      this.updateWithdrawRangeForSelectedAccount();
    },

    /**
     * 根据选中的账户更新提现金额范围
     */
    updateWithdrawRangeForSelectedAccount() {
      if (!this.selectedAccountInfo || !this.withdrawData || this.withdrawData.length === 0) {
        return;
      }

      // 根据选中账户的类型找到对应的提现配置
      const selectedAccountType = this.selectedAccountInfo.type;
      const configIndex = this.withdrawData.findIndex(
        (config) => config.account_type === selectedAccountType
      );

      if (configIndex !== -1) {
        // 找到对应配置，设置金额范围
        this.setWithdrawRangeValue(configIndex);
      } else {
        // 未找到对应配置，使用第一个作为默认值
        this.setWithdrawRangeValue(0);
      }
    },
    // 输入框 input事件
    handleCustomAmountInput(event: Event) {
      const input = event.target as HTMLInputElement;
      let value = input.value.replace(/\D/g, "") as string;

      // 如果输入为空，直接处理
      if (!value) {
        this.setSelectedAmount("");
        this.getVaildAmountErr("");
        return;
      }

      // 金额限制：确保不超过最大限额
      const numValue = Number(value);
      if (this.maxAmountNum && this.maxAmountNum > 0 && numValue > this.maxAmountNum) {
        value = this.maxAmountNum.toString();
        // 更新输入框显示值
        input.value = value;
      }
      this.setSelectedAmount(value);
    },
    // 点击选中
    setSelectedAmount(amount: string) {
      this.selectedAmount = amount;
      this.getVaildAmountErr(amount);
    },
    // 校验金额范围提示
    getVaildAmountErr(value: string | number) {
      const min = this.minAmountNum || 0;
      const max = this.maxAmountNum || 0;
      const globalStore = useGlobalStore();
      const availableBalance = globalStore.balance;
      const numValue = Number(value);

      // 重置提示状态
      this.errTip = "";
      this.warnTip = "";

      // 空值处理
      if (value === "" || value === null || value === undefined) {
        this.errTip = `Enter Amount ${min} - ${max}₱`;
        return this.errTip;
      }

      // 无效数值处理
      if (isNaN(numValue) || numValue < 0) {
        this.errTip = "Please enter a valid amount";
        return this.errTip;
      }

      // 余额不足检查（优先级最高）
      if (availableBalance < numValue) {
        this.errTip = "Insufficient Balance.";
        return this.errTip;
      }

      // 金额范围验证
      if (numValue < min) {
        this.errTip = `The minimum amount is ${min}₱`;
      } else if (numValue > max) {
        this.errTip = `The maximum amount is ${max}₱`;
      } else if (numValue === max) {
        // 达到最大限额时显示警告
        this.warnTip = `The maximum allowable input is ${max}₱`;
      }

      return this.errTip;
    },
    // 选择银行账户
    handleCardCheck(item: AccountInfo) {
      // 使用统一的账户设置方法
      this.setSelectedAccount(item);
      this.showAccountListDialog = false;
      // 保存用户选择到本地存储
      setLocalStorage(SELECT_ACCOUNT_TYPE, `${item.type}_${item.account_id}`);
    },
    // 设置提现的最大、最小范围
    setWithdrawRangeValue(index: number) {
      this.minAmountNum = this.withdrawData[index]?.min || null;
      this.maxAmountNum = this.withdrawData[index]?.max || null;
    },

    // 获取提现账户列表
    async getWithdrawAccountList() {
      const res = await getWithdrawAccounts();
      this.accounts = res.list;
      this.accountLimitNum = res.total_num;

      return res?.list || [];
    },
    // 获取提现方式 及金额配置
    async getWithdrawConfigList(callBack?: () => void) {
      const res = await rechargeWithdraw({
        appChannel: this.CHANEL_TYPE,
      });
      if (res && res.withdraw) {
        this.withdrawData = res.withdraw;
        for (let index = 0; index < this.withdrawData.length; index++) {
          this.withdrawData.sort((a, b) => {
            if (a.sort != b.sort) {
              //按sort值从大到小
              return b.sort - a.sort;
            } else {
              //sort值相同按照首字母从大到小排序
              if (a.name && b.name) {
                return b.name.localeCompare(a.name);
              }
              return 0;
            }
          });
        }

        callBack && callBack();
      }
      return this.withdrawData || [];
    },

    // 1倍流水风险校验
    async handleRiskWithdrawalDialog(callBack: () => void, jumpCheckRisk = false) {
      if (jumpCheckRisk) {
        callBack && callBack();
      } else {
        const response = await checkWithdrawRisk({});
        const { code, data } = response;
        this.riskWithdrawalInfo = data;
        this.riskWithdrawalCallBack = callBack;
        if (code === 200 || code === 0) {
          if (data.status === 0) {
            this.showRiskWithdrawalDialog = true;
          } else {
            if (this.kycMgrStore.kycType == KYC_TYPE.SIMPLE) {
              this.kycMgrStore.inSceneType = KYC_SCENE.Withdraw;
              // 弹第一次弹窗
              if (data?.kyc_status == 1) {
                if (data?.kyc_step == 2) {
                  this.kycMgrStore.reviewPop();
                } else {
                  this.showKycWithdralDialog = true;
                }
                return;
              }
              if (data?.kyc_status == 2) {
                // 弹第二次弹窗
                if (data?.kyc_step == 2) {
                  this.kycMgrStore.reviewPop();
                } else {
                  if (data?.kyc_step == 100) {
                    this.showKycWithdralDialog = true;
                  } else {
                    this.autoPopMgrStore.showKycVerifyTip = true;
                  }
                }
                return;
              }
            }
            callBack && callBack();
          }
        } else {
          callBack && callBack();
        }
      }
    },

    // 提现底部确认入口
    async handleConfirm() {
      try {
        // 小程序没有添加账号，只有当前账号
        if (this.isMiniChannel || this.accounts.length) {
          // this.kycMgrStore.verifyKyc(KYC_SCENE.Withdraw, () => {
          //   this.preVaildFn();
          // });
          this.preVaildFn();
        } else {
          this.showWithdrawDialog = false;
          router.push(`/account/withdraw-account`);
        }
      } catch (error) {
        console.error("Withdraw risk check failed:", error);
        this.$dialog({
          title: "Tips",
          message: "Failed to verify withdrawal conditions. Please try again.",
          confirmText: "Done",
          showCancelButton: false,
          onConfirm: () => {
            this.showWithdrawDialog = false;
          },
        });
      }
    },
    // 前置校验
    preVaildFn() {
      if (this.isMiniChannel) {
        // 二次确认弹窗
        this.showMoneyConfirmDialog = true;
      } else if (this.CHANEL_TYPE === CHANEL_TYPE.WEB) {
        if (this.isNeedLoginPassword) {
          const { verifyPreconditions } = useVerifyPreconditions();
          const validResult = verifyPreconditions(() => {});
          if (validResult) {
            this.showWithdrawDialog = false;
            this.showVerifyLoginPasswordDialog = true;
          } else {
            // 从没设置过登录密码
            this.showWithdrawDialog = false;
            this.showVerifyPreconditionsDialog = true;
          }
        } else {
          this.reqExchange();
        }
      }
    },
    // 密码次数过多限制，复制email
    handlePasswordLimtConfirm() {
      this.showPaymentPassworLimtDialog = false;
    },
    // 登录密码
    handleLoginPasswordSuccConfirm(loginPassword: string) {
      verifyLoginPassword({
        params: {
          withdraw_password: Md5.hashStr(loginPassword).toString(),
        },
        successCallBack: () => {
          this.showVerifyLoginPasswordDialog = false;
          this.reqExchange();
        },
        failCallBack: () => {},
      });
    },
    // 绑定手机号码、设置登录密码设置成功
    handlePasswordSuccConfirm() {
      this.showVerifyPreconditionsDialog = false;
      this.reqExchange();
    },
    // 小程序端提现弹窗 确认
    async handleMoneyConfirm() {
      this.showMoneyConfirmDialog = false;
      this.reqExchange();
    },
    // 验证
    reqExchange() {
      executeVerification(GEETEST_TYPE.withdraw, (result: VerificationResult | false) => {
        if (result && result.success) {
          const ret = {
            // Geetest 参数
            geetest_guard: result.data?.geetest_guard || "",
            userInfo: result.data?.userInfo || "",
            geetest_captcha: result.data?.geetest_captcha || "",
            buds: result.data?.buds || "64",
            // Cloudflare 参数
            "cf-token": result.data?.["cf-token"] || "",
            "cf-scene": result.data?.["cf-scene"] || "",
          };
          this.reqExchange_true(ret);
        }
      });
    },
    // 发起提现请求
    async reqExchange_true(ret?: any) {
      try {
        const globalStore = useGlobalStore();
        const params = {
          account_id: (this.selectedAccountInfo as any)?.type || "",
          product_type: PRODUCT_TYPE.WITHDRAW_GCASH_WEB,
          name: globalStore.userInfo.nickname,
          amount: parseInt(this.selectedAmount),
          price: parseInt(this.selectedAmount),
          quantity: 1,
          geetest_guard: ret?.geetest_guard || "",
          userInfo: ret?.userInfo || "",
          geetest_captcha: ret?.geetest_captcha || "",
          buds: ret?.buds || "64",
        };

        if (this.CHANEL_TYPE == CHANEL_TYPE.WEB) {
          if ((this.selectedAccountInfo as any)?.type == METHODS_ID.Maya) {
            params["product_type"] = PRODUCT_TYPE.WITHDRAW_MAYA_WEB;
          } else if ((this.selectedAccountInfo as any)?.type == METHODS_ID.Gcash) {
            params["product_type"] = PRODUCT_TYPE.WITHDRAW_GCASH_WEB;
          }
        }

        if (this.CHANEL_TYPE == CHANEL_TYPE.G_CASH) {
          params["product_type"] = PRODUCT_TYPE.GCASH;
          params["app_package_name"] = "com.nustargame.gcash";
        }

        if (this.CHANEL_TYPE == CHANEL_TYPE.MAYA) {
          params["product_type"] = PRODUCT_TYPE.MAYA_MINI;
        }

        const response = await goExchange(params);
        const { code, msg } = response;

        if (code === 200 || code === 0) {
          this.showWithdrawDialog = false;
          this.$dialog({
            title: "Congratulations",
            message:
              "Your withdrawal request has been submitted successfully. You will receive in your account within 10 minutes.",
            confirmText: "Done",
            showCancelButton: false,
            onConfirm: async () => {
              // 更新余额
              globalStore.getBalance();
            },
          });
        } else {
          if (code == MAINTENANCETIPCODE) {
            // 服务器维护页
            this.showWithdrawDialog = false;
            router.push("/system/maintenance");
          } else if (code == 2) {
            // 需要maya登入
            await MobileWindowManager.launchExternalGame(
              async () => response?.url,
              (error) => {
                showToast("Failed to open Maya , please try again");
              }
            );
          } else if (code == 1) {
            this.$dialog({
              title: "Tips",
              message: msg || "Some mistakes occurred.",
              confirmText: "Done",
              showCancelButton: false,
              onConfirm: async () => {},
            });
            this.showWithdrawDialog = false;
          } else {
            this.$dialog({
              title: "Tips",
              message: msg || "Some mistakes occurred.",
              confirmText: "Done",
              showCancelButton: false,
              onConfirm: async () => {},
            });
            this.showWithdrawDialog = false;
          }
        }
      } catch (error) {
        console.error("Withdrawal request failed:", error);
        this.showWithdrawDialog = false;
        this.$dialog({
          title: "Tips",
          message: "Network error occurred. Please check your connection and try again.",
          confirmText: "Done",
          showCancelButton: false,
          onConfirm: () => {
            // 可以选择重新打开弹窗或其他操作
          },
        });
      }
    },

    /**拉取不到数据或用户未配置/未选择提现账号 点击按钮给出提示信息 */
    handleInputClick() {
      if (!this.withdrawData || this.withdrawData.length === 0) {
        showToast({
          message: enTranslations.tipword101,
          className: "custom-toast-width",
        });
        return;
      }
      if (!this.accounts || this.accounts.length === 0) {
        showToast({
          message: enTranslations.tipword99,
        });
        return;
      }
      let filter_list = this.withdrawData.filter((item) => {
        return item.account_type == this.selectedAccountInfo.type;
      });
      if (filter_list.length == 0) {
        let tips_msg = enTranslations.tipword102;
        if (this.CHANEL_TYPE == CHANEL_TYPE.G_CASH) {
          tips_msg = enTranslations.tipword103;
        }
        showToast({
          message: tips_msg,
        });
        return;
      }
    },

    resetData() {
      this.selectedAmount = "";
      this.errTip = "";
    },
  },
});
